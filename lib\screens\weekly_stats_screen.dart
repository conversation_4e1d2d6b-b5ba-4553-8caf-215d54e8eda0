import 'package:flutter/material.dart';

class WeeklyStatsScreen extends StatelessWidget {
  const WeeklyStatsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(title: const Text('This Week')),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildWeeklySummary(context),
          const SizedBox(height: 24),
          Text(
            'Daily Breakdown',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildDailyProgress(context),
          const SizedBox(height: 24),
          Text(
            'This Week\'s Workouts',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildWorkoutCard(
            context,
            'Monday',
            'Upper Body',
            '45 min',
            Icons.fitness_center,
            colorScheme.primary,
          ),
          const Sized<PERSON>ox(height: 12),
          _buildWorkoutCard(
            context,
            'Wednesday',
            'Lower Body',
            '60 min',
            Icons.fitness_center,
            colorScheme.primary,
          ),
          const SizedBox(height: 12),
          _buildWorkoutCard(
            context,
            'Friday',
            'Cardio',
            '30 min',
            Icons.directions_run,
            colorScheme.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildWeeklySummary(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Weekly Progress',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildCircularProgress(
                  context,
                  'Workouts',
                  '3/5',
                  0.6,
                  Colors.blue,
                ),
                _buildCircularProgress(
                  context,
                  'Duration',
                  '135 min',
                  0.45,
                  Colors.green,
                ),
                _buildCircularProgress(
                  context,
                  'Calories',
                  '850',
                  0.7,
                  Colors.orange,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCircularProgress(
    BuildContext context,
    String label,
    String value,
    double progress,
    Color color,
  ) {
    return Column(
      children: [
        SizedBox(
          height: 80,
          width: 80,
          child: Stack(
            children: [
              CircularProgressIndicator(
                value: progress,
                backgroundColor: color.withAlpha(51), // 0.2 * 255 = 51
                valueColor: AlwaysStoppedAnimation<Color>(color),
                strokeWidth: 8,
              ),
              Center(
                child: Text(
                  value,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(label, style: Theme.of(context).textTheme.bodyMedium),
      ],
    );
  }

  Widget _buildDailyProgress(BuildContext context) {
    final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    final completed = [true, false, true, false, true, false, false];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: List.generate(
            7,
            (index) =>
                _buildDayIndicator(context, days[index], completed[index]),
          ),
        ),
      ),
    );
  }

  Widget _buildDayIndicator(BuildContext context, String day, bool completed) {
    return Column(
      children: [
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color:
                completed
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surface,
          ),
          child: Icon(
            completed ? Icons.check : Icons.close,
            color:
                completed
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurface,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),
        Text(day, style: Theme.of(context).textTheme.bodyMedium),
      ],
    );
  }

  Widget _buildWorkoutCard(
    BuildContext context,
    String day,
    String workout,
    String duration,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withAlpha(51), // 0.2 * 255 = 51
          child: Icon(icon, color: color),
        ),
        title: Text(workout),
        subtitle: Text(day),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            duration,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        onTap: () {
          // Navigate to workout details
        },
      ),
    );
  }
}

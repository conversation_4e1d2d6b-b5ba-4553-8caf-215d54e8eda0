import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/database_service.dart';
import '../models/workout.dart';

class WeeklyStatsScreen extends StatefulWidget {
  const WeeklyStatsScreen({super.key});

  @override
  State<WeeklyStatsScreen> createState() => _WeeklyStatsScreenState();
}

class _WeeklyStatsScreenState extends State<WeeklyStatsScreen> {
  final DatabaseService _databaseService = DatabaseService();
  Map<String, dynamic> _stats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  Future<void> _loadStats() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      try {
        final stats = await _databaseService.getDetailedWeeklyStats(user.uid);
        setState(() {
          _stats = stats;
          _isLoading = false;
        });
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(title: const Text('This Week')),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : ListView(
                padding: const EdgeInsets.all(16.0),
                children: [
                  _buildWeeklySummary(context),
                  const SizedBox(height: 24),
                  Text(
                    'Daily Breakdown',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  _buildDailyProgress(context),
                  const SizedBox(height: 24),
                  Text(
                    'This Week\'s Workouts',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  ..._buildWorkoutCards(context, colorScheme),
                ],
              ),
    );
  }

  Widget _buildWeeklySummary(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Weekly Progress',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildCircularProgress(
                  context,
                  'Workouts',
                  '${_stats['workoutCount'] ?? 0}/${_stats['workoutGoal'] ?? 5}',
                  (_stats['workoutProgress'] ?? 0.0).toDouble(),
                  Colors.blue,
                ),
                _buildCircularProgress(
                  context,
                  'Duration',
                  '${_stats['totalDuration'] ?? 0} min',
                  (_stats['durationProgress'] ?? 0.0).toDouble(),
                  Colors.green,
                ),
                _buildCircularProgress(
                  context,
                  'Calories',
                  '${_stats['totalCalories'] ?? 0}',
                  (_stats['caloriesProgress'] ?? 0.0).toDouble(),
                  Colors.orange,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCircularProgress(
    BuildContext context,
    String label,
    String value,
    double progress,
    Color color,
  ) {
    return Column(
      children: [
        SizedBox(
          height: 80,
          width: 80,
          child: Stack(
            children: [
              CircularProgressIndicator(
                value: progress,
                backgroundColor: color.withAlpha(51), // 0.2 * 255 = 51
                valueColor: AlwaysStoppedAnimation<Color>(color),
                strokeWidth: 8,
              ),
              Center(
                child: Text(
                  value,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(label, style: Theme.of(context).textTheme.bodyMedium),
      ],
    );
  }

  Widget _buildDailyProgress(BuildContext context) {
    final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    final completed =
        _stats['dailyCompletion'] as List<dynamic>? ?? List.filled(7, false);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: List.generate(
            7,
            (index) => _buildDayIndicator(
              context,
              days[index],
              completed[index] as bool? ?? false,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDayIndicator(BuildContext context, String day, bool completed) {
    return Column(
      children: [
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color:
                completed
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surface,
          ),
          child: Icon(
            completed ? Icons.check : Icons.close,
            color:
                completed
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurface,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),
        Text(day, style: Theme.of(context).textTheme.bodyMedium),
      ],
    );
  }

  List<Widget> _buildWorkoutCards(
    BuildContext context,
    ColorScheme colorScheme,
  ) {
    final workouts = _stats['workouts'] as List<dynamic>? ?? [];

    if (workouts.isEmpty) {
      return [
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Center(
              child: Text(
                'No workouts this week yet',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ),
        ),
      ];
    }

    return workouts.map<Widget>((workout) {
      final workoutObj = workout as Workout;
      final dayNames = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday',
      ];
      final dayName = dayNames[workoutObj.date.weekday - 1];

      return Padding(
        padding: const EdgeInsets.only(bottom: 12.0),
        child: _buildWorkoutCard(
          context,
          dayName,
          workoutObj.name,
          '45 min', // Default duration
          _getWorkoutIcon(workoutObj.category ?? 'strength'),
          colorScheme.primary,
        ),
      );
    }).toList();
  }

  IconData _getWorkoutIcon(String category) {
    switch (category.toLowerCase()) {
      case 'cardio':
        return Icons.directions_run;
      case 'strength':
        return Icons.fitness_center;
      case 'flexibility':
        return Icons.self_improvement;
      default:
        return Icons.fitness_center;
    }
  }

  Widget _buildWorkoutCard(
    BuildContext context,
    String day,
    String workout,
    String duration,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withAlpha(51), // 0.2 * 255 = 51
          child: Icon(icon, color: color),
        ),
        title: Text(workout),
        subtitle: Text(day),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            duration,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        onTap: () {
          // Navigate to workout details
        },
      ),
    );
  }
}

class UserProfile {
  final String uid;
  final String email;
  final String? displayName;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  
  // Fitness data
  final int totalWorkouts;
  final int currentStreak;
  final int longestStreak;
  final double totalWeightLifted;
  final int totalExercises;
  
  // Goals
  final int? weeklyWorkoutGoal;
  final double? weightGoal;
  final String? fitnessGoal; // 'lose_weight', 'gain_muscle', 'maintain', etc.
  
  // Preferences
  final List<String> favoriteExercises;
  final String? preferredWorkoutTime; // 'morning', 'afternoon', 'evening'
  final int? workoutDuration; // preferred duration in minutes

  UserProfile({
    required this.uid,
    required this.email,
    this.displayName,
    required this.createdAt,
    required this.lastLoginAt,
    this.totalWorkouts = 0,
    this.currentStreak = 0,
    this.longestStreak = 0,
    this.totalWeightLifted = 0.0,
    this.totalExercises = 0,
    this.weeklyWorkoutGoal,
    this.weightGoal,
    this.fitnessGoal,
    this.favoriteExercises = const [],
    this.preferredWorkoutTime,
    this.workoutDuration,
  });

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'email': email,
      'displayName': displayName,
      'createdAt': createdAt,
      'lastLoginAt': lastLoginAt,
      'totalWorkouts': totalWorkouts,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'totalWeightLifted': totalWeightLifted,
      'totalExercises': totalExercises,
      'weeklyWorkoutGoal': weeklyWorkoutGoal,
      'weightGoal': weightGoal,
      'fitnessGoal': fitnessGoal,
      'favoriteExercises': favoriteExercises,
      'preferredWorkoutTime': preferredWorkoutTime,
      'workoutDuration': workoutDuration,
    };
  }

  // Create from Firestore Map
  factory UserProfile.fromMap(Map<String, dynamic> map) {
    return UserProfile(
      uid: map['uid'] ?? '',
      email: map['email'] ?? '',
      displayName: map['displayName'],
      createdAt: map['createdAt']?.toDate() ?? DateTime.now(),
      lastLoginAt: map['lastLoginAt']?.toDate() ?? DateTime.now(),
      totalWorkouts: map['totalWorkouts'] ?? 0,
      currentStreak: map['currentStreak'] ?? 0,
      longestStreak: map['longestStreak'] ?? 0,
      totalWeightLifted: (map['totalWeightLifted'] ?? 0.0).toDouble(),
      totalExercises: map['totalExercises'] ?? 0,
      weeklyWorkoutGoal: map['weeklyWorkoutGoal'],
      weightGoal: map['weightGoal']?.toDouble(),
      fitnessGoal: map['fitnessGoal'],
      favoriteExercises: List<String>.from(map['favoriteExercises'] ?? []),
      preferredWorkoutTime: map['preferredWorkoutTime'],
      workoutDuration: map['workoutDuration'],
    );
  }

  // Copy with method for updates
  UserProfile copyWith({
    String? uid,
    String? email,
    String? displayName,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    int? totalWorkouts,
    int? currentStreak,
    int? longestStreak,
    double? totalWeightLifted,
    int? totalExercises,
    int? weeklyWorkoutGoal,
    double? weightGoal,
    String? fitnessGoal,
    List<String>? favoriteExercises,
    String? preferredWorkoutTime,
    int? workoutDuration,
  }) {
    return UserProfile(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      totalWorkouts: totalWorkouts ?? this.totalWorkouts,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      totalWeightLifted: totalWeightLifted ?? this.totalWeightLifted,
      totalExercises: totalExercises ?? this.totalExercises,
      weeklyWorkoutGoal: weeklyWorkoutGoal ?? this.weeklyWorkoutGoal,
      weightGoal: weightGoal ?? this.weightGoal,
      fitnessGoal: fitnessGoal ?? this.fitnessGoal,
      favoriteExercises: favoriteExercises ?? this.favoriteExercises,
      preferredWorkoutTime: preferredWorkoutTime ?? this.preferredWorkoutTime,
      workoutDuration: workoutDuration ?? this.workoutDuration,
    );
  }
}

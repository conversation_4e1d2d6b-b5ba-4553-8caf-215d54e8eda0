class Goal {
  final String id;
  final String userId;
  final String title;
  final String description;
  final String type; // 'workout_frequency', 'weight_goal', 'strength_goal', 'cardio_goal'
  final double targetValue;
  final double currentValue;
  final String unit; // 'workouts', 'kg', 'minutes', etc.
  final DateTime createdAt;
  final DateTime targetDate;
  final bool isCompleted;
  final String? exerciseName; // For exercise-specific goals

  Goal({
    required this.id,
    required this.userId,
    required this.title,
    required this.description,
    required this.type,
    required this.targetValue,
    this.currentValue = 0.0,
    required this.unit,
    required this.createdAt,
    required this.targetDate,
    this.isCompleted = false,
    this.exerciseName,
  });

  // Calculate progress percentage
  double get progressPercentage {
    if (targetValue <= 0) return 0.0;
    return (currentValue / targetValue).clamp(0.0, 1.0);
  }

  // Check if goal is overdue
  bool get isOverdue {
    return DateTime.now().isAfter(targetDate) && !isCompleted;
  }

  // Days remaining
  int get daysRemaining {
    final difference = targetDate.difference(DateTime.now()).inDays;
    return difference > 0 ? difference : 0;
  }

  // Progress text
  String get progressText {
    if (type == 'workout_frequency') {
      return '${currentValue.toInt()}/${targetValue.toInt()} $unit';
    } else {
      return '${currentValue.toStringAsFixed(1)}/${targetValue.toStringAsFixed(1)} $unit';
    }
  }

  // Convert to map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'title': title,
      'description': description,
      'type': type,
      'targetValue': targetValue,
      'currentValue': currentValue,
      'unit': unit,
      'createdAt': createdAt,
      'targetDate': targetDate,
      'isCompleted': isCompleted,
      'exerciseName': exerciseName,
    };
  }

  // Create from map (Firestore)
  factory Goal.fromMap(String id, Map<String, dynamic> map) {
    return Goal(
      id: id,
      userId: map['userId'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      type: map['type'] ?? '',
      targetValue: (map['targetValue'] ?? 0.0).toDouble(),
      currentValue: (map['currentValue'] ?? 0.0).toDouble(),
      unit: map['unit'] ?? '',
      createdAt: map['createdAt']?.toDate() ?? DateTime.now(),
      targetDate: map['targetDate']?.toDate() ?? DateTime.now(),
      isCompleted: map['isCompleted'] ?? false,
      exerciseName: map['exerciseName'],
    );
  }

  // Copy with method for updates
  Goal copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    String? type,
    double? targetValue,
    double? currentValue,
    String? unit,
    DateTime? createdAt,
    DateTime? targetDate,
    bool? isCompleted,
    String? exerciseName,
  }) {
    return Goal(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      targetValue: targetValue ?? this.targetValue,
      currentValue: currentValue ?? this.currentValue,
      unit: unit ?? this.unit,
      createdAt: createdAt ?? this.createdAt,
      targetDate: targetDate ?? this.targetDate,
      isCompleted: isCompleted ?? this.isCompleted,
      exerciseName: exerciseName ?? this.exerciseName,
    );
  }

  @override
  String toString() {
    return 'Goal(id: $id, title: $title, progress: ${progressPercentage * 100}%)';
  }
}

import 'package:flutter/material.dart';
import '../models/goal.dart';
import '../services/database_service.dart';
import '../services/auth_service.dart';
import 'set_goal_screen.dart';

class GoalStatsScreen extends StatefulWidget {
  const GoalStatsScreen({super.key});

  @override
  State<GoalStatsScreen> createState() => _GoalStatsScreenState();
}

class _GoalStatsScreenState extends State<GoalStatsScreen> {
  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();
  bool _isLoading = true;
  List<Goal> _activeGoals = [];
  List<Goal> _completedGoals = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadGoals();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh goals when returning to this screen
    if (ModalRoute.of(context)?.isCurrent == true) {
      _loadGoals();
    }
  }

  Future<void> _loadGoals() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    final user = _authService.currentUser;
    if (user == null) {
      setState(() {
        _errorMessage = 'User not logged in';
        _isLoading = false;
      });
      return;
    }

    try {
      final activeGoalsStream = _databaseService.getActiveGoals(user.uid);
      final completedGoalsStream = _databaseService.getCompletedGoals(user.uid);

      final activeGoals = await activeGoalsStream.first;
      final completedGoals = await completedGoalsStream.first;

      setState(() {
        _activeGoals = activeGoals;
        _completedGoals = completedGoals;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading goals: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Goal Progress'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadGoals,
            tooltip: 'Refresh Goals',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _errorMessage = null;
                });
                _loadGoals();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadGoals,
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildGoalSummary(context),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Active Goals',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              TextButton.icon(
                onPressed: () async {
                  final result = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SetGoalScreen(),
                    ),
                  );
                  if (result == true) {
                    _loadGoals(); // Refresh goals after adding new one
                  }
                },
                icon: const Icon(Icons.add),
                label: const Text('New Goal'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_activeGoals.isEmpty)
            _buildNoGoalsMessage(
              'No active goals yet',
              'Set your first goal to get started!',
            )
          else
            ..._activeGoals.map(
              (goal) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildGoalCard(context, goal),
              ),
            ),
          const SizedBox(height: 24),
          Text(
            'Completed Goals',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          if (_completedGoals.isEmpty)
            _buildNoGoalsMessage(
              'No completed goals yet',
              'Complete your first goal to see it here!',
            )
          else
            ..._completedGoals.map(
              (goal) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildCompletedGoalCard(context, goal),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNoGoalsMessage(String title, String subtitle) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            Icon(
              Icons.flag_outlined,
              size: 48,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalSummary(BuildContext context) {
    // Calculate average progress
    double averageProgress = 0.0;
    if (_activeGoals.isNotEmpty) {
      averageProgress =
          _activeGoals.fold(0.0, (sum, goal) => sum + goal.progressPercentage) /
          _activeGoals.length;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Goal Overview',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildGoalOverviewItem(
                  context,
                  'Active',
                  '${_activeGoals.length}',
                  Colors.blue,
                ),
                _buildGoalOverviewItem(
                  context,
                  'Completed',
                  '${_completedGoals.length}',
                  Colors.green,
                ),
                _buildGoalOverviewItem(
                  context,
                  'Progress',
                  '${(averageProgress * 100).round()}%',
                  Colors.orange,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalOverviewItem(
    BuildContext context,
    String label,
    String value,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color.withAlpha(26),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(label, style: Theme.of(context).textTheme.bodyMedium),
      ],
    );
  }

  Widget _buildGoalCard(BuildContext context, Goal goal) {
    final color = _getGoalColor(goal.type);
    final daysLeft = goal.daysRemaining;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    goal.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color:
                        goal.isOverdue
                            ? Colors.red.withAlpha(51)
                            : color.withAlpha(51),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    goal.isOverdue ? 'Overdue' : '$daysLeft days left',
                    style: TextStyle(
                      fontSize: 12,
                      color: goal.isOverdue ? Colors.red : color,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              goal.description,
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: goal.progressPercentage,
                    backgroundColor: color.withAlpha(51),
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                    minHeight: 8,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  goal.progressText,
                  style: TextStyle(fontWeight: FontWeight.bold, color: color),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompletedGoalCard(BuildContext context, Goal goal) {
    final color = _getGoalColor(goal.type);
    final icon = _getGoalIcon(goal.type);
    final formattedDate =
        '${goal.targetDate.day}/${goal.targetDate.month}/${goal.targetDate.year}';

    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withAlpha(77),
          child: Icon(icon, color: color),
        ),
        title: Row(
          children: [
            Expanded(child: Text(goal.title)),
            const SizedBox(width: 8),
            const Icon(Icons.check_circle, color: Colors.green, size: 16),
          ],
        ),
        subtitle: Text(goal.description),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              'Completed',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            Text(
              formattedDate,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getGoalColor(String goalType) {
    switch (goalType) {
      case 'workout_frequency':
        return Colors.blue;
      case 'weight_goal':
        return Colors.orange;
      case 'strength_goal':
        return Colors.red;
      case 'cardio_goal':
        return Colors.green;
      default:
        return Colors.blue;
    }
  }

  IconData _getGoalIcon(String goalType) {
    switch (goalType) {
      case 'workout_frequency':
        return Icons.fitness_center;
      case 'weight_goal':
        return Icons.monitor_weight;
      case 'strength_goal':
        return Icons.sports_gymnastics;
      case 'cardio_goal':
        return Icons.directions_run;
      default:
        return Icons.flag;
    }
  }
}

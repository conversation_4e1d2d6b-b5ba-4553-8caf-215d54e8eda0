import 'package:flutter/material.dart';

class StreakStatsScreen extends StatelessWidget {
  const StreakStatsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Workout Streak'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildStreakSummary(context),
          const Sized<PERSON>ox(height: 24),
          Text(
            'Streak History',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildStreakCalendar(context),
          const SizedBox(height: 24),
          Text(
            'Achievements',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildAchievementCard(
            context,
            'Consistency Champion',
            '5-day streak achieved',
            Icons.emoji_events,
            colorScheme.primary,
            true,
          ),
          const Sized<PERSON><PERSON>(height: 12),
          _buildAchievementCard(
            context,
            'Workout Warrior',
            '10-day streak',
            Icons.military_tech,
            colorScheme.primary,
            false,
          ),
          const Sized<PERSON>ox(height: 12),
          _buildAchievementCard(
            context,
            'Fitness Fanatic',
            '30-day streak',
            Icons.star,
            colorScheme.primary,
            false,
          ),
        ],
      ),
    );
  }

  Widget _buildStreakSummary(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'Current Streak',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                Text(
                  '5',
                  style: Theme.of(context).textTheme.displayLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(width: 8),
                Text(
                  'days',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildStreakStat(context, 'Best Streak', '12 days'),
                _buildStreakStat(context, 'This Month', '18 days'),
                _buildStreakStat(context, 'Total', '45 days'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStreakStat(BuildContext context, String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
      ],
    );
  }

  Widget _buildStreakCalendar(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'June 2023',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.chevron_left),
                      onPressed: () {},
                      iconSize: 20,
                    ),
                    IconButton(
                      icon: const Icon(Icons.chevron_right),
                      onPressed: () {},
                      iconSize: 20,
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 250,
              child: Center(
                child: Text(
                  'Calendar will be displayed here',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
    bool unlocked,
  ) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withAlpha(unlocked ? 255 : 77), // 0.3 * 255 = 76.5 ≈ 77
          child: Icon(
            icon,
            color: unlocked ? Colors.white : Colors.white.withAlpha(128), // 0.5 * 255 = 127.5 ≈ 128
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: unlocked
                ? Theme.of(context).colorScheme.onSurface
                : Theme.of(context).colorScheme.onSurface.withAlpha(128), // 0.5 * 255 = 127.5 ≈ 128
          ),
        ),
        subtitle: Text(
          description,
          style: TextStyle(
            color: unlocked
                ? Theme.of(context).colorScheme.onSurfaceVariant
                : Theme.of(context).colorScheme.onSurfaceVariant.withAlpha(128), // 0.5 * 255 = 127.5 ≈ 128
          ),
        ),
        trailing: unlocked
            ? const Icon(Icons.check_circle, color: Colors.green)
            : const Icon(Icons.lock, color: Colors.grey),
      ),
    );
  }
}







import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../services/database_service.dart';
import '../models/user_profile.dart';

class StreakStatsScreen extends StatefulWidget {
  const StreakStatsScreen({super.key});

  @override
  State<StreakStatsScreen> createState() => _StreakStatsScreenState();
}

class _StreakStatsScreenState extends State<StreakStatsScreen> {
  final AuthService _authService = AuthService();
  final DatabaseService _databaseService = DatabaseService();

  UserProfile? _userProfile;
  bool _isLoading = true;
  Map<String, dynamic> _streakStats = {};

  @override
  void initState() {
    super.initState();
    _loadStreakData();
  }

  Future<void> _loadStreakData() async {
    final user = _authService.currentUser;
    if (user != null) {
      try {
        final profile = await _databaseService.getUserProfileObject(user.uid);
        final streakStats = await _databaseService.getStreakStats(user.uid);

        setState(() {
          _userProfile = profile;
          _streakStats = streakStats;
          _isLoading = false;
        });
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(title: const Text('Workout Streak')),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : ListView(
                padding: const EdgeInsets.all(16.0),
                children: [
                  _buildStreakSummary(context),
                  const SizedBox(height: 24),
                  Text(
                    'Streak History',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  _buildStreakCalendar(context),
                  const SizedBox(height: 24),
                  Text(
                    'Achievements',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  _buildAchievementCard(
                    context,
                    'Consistency Champion',
                    '5-day streak achieved',
                    Icons.emoji_events,
                    colorScheme.primary,
                    (_userProfile?.currentStreak ?? 0) >= 5,
                  ),
                  const SizedBox(height: 12),
                  _buildAchievementCard(
                    context,
                    'Workout Warrior',
                    '10-day streak',
                    Icons.military_tech,
                    colorScheme.primary,
                    (_userProfile?.currentStreak ?? 0) >= 10,
                  ),
                  const SizedBox(height: 12),
                  _buildAchievementCard(
                    context,
                    'Fitness Fanatic',
                    '30-day streak',
                    Icons.star,
                    colorScheme.primary,
                    (_userProfile?.currentStreak ?? 0) >= 30,
                  ),
                ],
              ),
    );
  }

  Widget _buildStreakSummary(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'Current Streak',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                Text(
                  '${_userProfile?.currentStreak ?? 0}',
                  style: Theme.of(context).textTheme.displayLarge?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                Text('days', style: Theme.of(context).textTheme.headlineSmall),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildStreakStat(
                  context,
                  'Best Streak',
                  '${_userProfile?.longestStreak ?? 0} days',
                ),
                _buildStreakStat(
                  context,
                  'Total Workouts',
                  '${_userProfile?.totalWorkouts ?? 0}',
                ),
                _buildStreakStat(
                  context,
                  'This Week',
                  '${_streakStats['weeklyWorkouts'] ?? 0}',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStreakStat(BuildContext context, String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildStreakCalendar(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'June 2023',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.chevron_left),
                      onPressed: () {},
                      iconSize: 20,
                    ),
                    IconButton(
                      icon: const Icon(Icons.chevron_right),
                      onPressed: () {},
                      iconSize: 20,
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 250,
              child: Center(
                child: Text(
                  'Calendar will be displayed here',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
    bool unlocked,
  ) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withAlpha(
            unlocked ? 255 : 77,
          ), // 0.3 * 255 = 76.5 ≈ 77
          child: Icon(
            icon,
            color:
                unlocked
                    ? Colors.white
                    : Colors.white.withAlpha(128), // 0.5 * 255 = 127.5 ≈ 128
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color:
                unlocked
                    ? Theme.of(context).colorScheme.onSurface
                    : Theme.of(context).colorScheme.onSurface.withAlpha(
                      128,
                    ), // 0.5 * 255 = 127.5 ≈ 128
          ),
        ),
        subtitle: Text(
          description,
          style: TextStyle(
            color:
                unlocked
                    ? Theme.of(context).colorScheme.onSurfaceVariant
                    : Theme.of(context).colorScheme.onSurfaceVariant.withAlpha(
                      128,
                    ), // 0.5 * 255 = 127.5 ≈ 128
          ),
        ),
        trailing:
            unlocked
                ? const Icon(Icons.check_circle, color: Colors.green)
                : const Icon(Icons.lock, color: Colors.grey),
      ),
    );
  }
}

class Exercise {
  final String name;
  final String description;
  final String category;
  final String type;
  final String? imageUrl;

  Exercise({
    required this.name,
    required this.description,
    required this.category,
    required this.type,
    this.imageUrl,
  });
}

class ExerciseData {
  static Map<String, String> getCategoryImagePaths() {
    return {
      'Strength': 'assets/images/strength.jpg',
      'HIIT': 'assets/images/hiit.jpg',
      'Cardio': 'assets/images/cardio.jpg',
      'Flexibility': 'assets/images/flexibility.jpg',
    };
  }

  // Get types for each category
  static Map<String, List<String>> getCategoryTypes() {
    return {
      'Strength': ['Upper Body', 'Lower Body', 'Core', 'Full Body'],
      'HIIT': ['Beginner HIIT', 'Intermediate HIIT', 'Advanced HIIT', 'Tabata'],
      'Cardio': ['Low Impact', 'High Impact', 'Endurance', 'Sprint'],
      'Flexibility': ['Yoga', 'Stretching', 'Mobility', 'Recovery'],
    };
  }

  // Get all exercises organized by category and type
  static Map<String, Map<String, List<Exercise>>> getCategoryTypeExercises() {
    return {
      'Strength': {
        'Upper Body': [
          Exercise(
            name: 'Push-ups',
            description: 'Classic chest and triceps exercise',
            category: 'Strength',
            type: 'Upper Body',
          ),
          Exercise(
            name: 'Pull-ups',
            description: 'Great for back and biceps',
            category: 'Strength',
            type: 'Upper Body',
          ),
          Exercise(
            name: 'Bench Press',
            description: 'Compound exercise for chest, shoulders, and triceps',
            category: 'Strength',
            type: 'Upper Body',
          ),
          Exercise(
            name: 'Shoulder Press',
            description: 'Builds shoulder and arm strength',
            category: 'Strength',
            type: 'Upper Body',
          ),
        ],
        'Lower Body': [
          Exercise(
            name: 'Squats',
            description: 'Fundamental lower body exercise',
            category: 'Strength',
            type: 'Lower Body',
          ),
          Exercise(
            name: 'Deadlifts',
            description: 'Full posterior chain exercise',
            category: 'Strength',
            type: 'Lower Body',
          ),
          Exercise(
            name: 'Lunges',
            description: 'Unilateral leg strengthening',
            category: 'Strength',
            type: 'Lower Body',
          ),
          Exercise(
            name: 'Leg Press',
            description: 'Machine-based leg exercise',
            category: 'Strength',
            type: 'Lower Body',
          ),
        ],
        'Core': [
          Exercise(
            name: 'Planks',
            description: 'Isometric core strengthening',
            category: 'Strength',
            type: 'Core',
          ),
          Exercise(
            name: 'Crunches',
            description: 'Basic abdominal exercise',
            category: 'Strength',
            type: 'Core',
          ),
          Exercise(
            name: 'Russian Twists',
            description: 'Oblique strengthening exercise',
            category: 'Strength',
            type: 'Core',
          ),
        ],
        'Full Body': [
          Exercise(
            name: 'Burpees',
            description: 'Full body compound movement',
            category: 'Strength',
            type: 'Full Body',
          ),
          Exercise(
            name: 'Thrusters',
            description: 'Squat to overhead press',
            category: 'Strength',
            type: 'Full Body',
          ),
        ],
      },
      'HIIT': {
        'Beginner HIIT': [
          Exercise(
            name: 'Jumping Jacks',
            description: 'Simple full body cardio',
            category: 'HIIT',
            type: 'Beginner HIIT',
          ),
          Exercise(
            name: 'High Knees',
            description: 'Running in place with high knees',
            category: 'HIIT',
            type: 'Beginner HIIT',
          ),
          Exercise(
            name: 'Butt Kicks',
            description: 'Running in place kicking heels to glutes',
            category: 'HIIT',
            type: 'Beginner HIIT',
          ),
        ],
        'Intermediate HIIT': [
          Exercise(
            name: 'Mountain Climbers',
            description: 'Great for core and cardio',
            category: 'HIIT',
            type: 'Intermediate HIIT',
          ),
          Exercise(
            name: 'Burpees',
            description: 'Full body exercise for strength and cardio',
            category: 'HIIT',
            type: 'Intermediate HIIT',
          ),
          Exercise(
            name: 'Jump Squats',
            description: 'Explosive lower body exercise',
            category: 'HIIT',
            type: 'Intermediate HIIT',
          ),
        ],
        'Advanced HIIT': [
          Exercise(
            name: 'Burpee Box Jumps',
            description: 'Advanced full body explosive movement',
            category: 'HIIT',
            type: 'Advanced HIIT',
          ),
          Exercise(
            name: 'Tuck Jumps',
            description: 'High intensity plyometric exercise',
            category: 'HIIT',
            type: 'Advanced HIIT',
          ),
        ],
        'Tabata': [
          Exercise(
            name: 'Tabata Squats',
            description: '20 seconds on, 10 seconds off squat protocol',
            category: 'HIIT',
            type: 'Tabata',
          ),
          Exercise(
            name: 'Tabata Push-ups',
            description: '20 seconds on, 10 seconds off push-up protocol',
            category: 'HIIT',
            type: 'Tabata',
          ),
        ],
      },
      'Cardio': {
        'Low Impact': [
          Exercise(
            name: 'Walking',
            description: 'Low impact steady state cardio',
            category: 'Cardio',
            type: 'Low Impact',
          ),
          Exercise(
            name: 'Swimming',
            description: 'Full body low impact exercise',
            category: 'Cardio',
            type: 'Low Impact',
          ),
          Exercise(
            name: 'Elliptical',
            description: 'Machine-based low impact cardio',
            category: 'Cardio',
            type: 'Low Impact',
          ),
        ],
        'High Impact': [
          Exercise(
            name: 'Running',
            description: 'High impact cardiovascular exercise',
            category: 'Cardio',
            type: 'High Impact',
          ),
          Exercise(
            name: 'Jump Rope',
            description: 'High intensity cardio and coordination',
            category: 'Cardio',
            type: 'High Impact',
          ),
        ],
        'Endurance': [
          Exercise(
            name: 'Long Distance Running',
            description: 'Extended cardiovascular endurance',
            category: 'Cardio',
            type: 'Endurance',
          ),
          Exercise(
            name: 'Cycling',
            description: 'Low-impact endurance exercise',
            category: 'Cardio',
            type: 'Endurance',
          ),
        ],
        'Sprint': [
          Exercise(
            name: 'Sprint Intervals',
            description: 'High intensity short bursts',
            category: 'Cardio',
            type: 'Sprint',
          ),
          Exercise(
            name: 'Hill Sprints',
            description: 'Uphill sprint training',
            category: 'Cardio',
            type: 'Sprint',
          ),
        ],
      },
      'Flexibility': {
        'Yoga': [
          Exercise(
            name: 'Sun Salutation',
            description: 'Classic yoga flow sequence',
            category: 'Flexibility',
            type: 'Yoga',
          ),
          Exercise(
            name: 'Warrior Poses',
            description: 'Standing yoga poses for strength and flexibility',
            category: 'Flexibility',
            type: 'Yoga',
          ),
          Exercise(
            name: 'Downward Dog',
            description: 'Full body stretch and strengthening pose',
            category: 'Flexibility',
            type: 'Yoga',
          ),
        ],
        'Stretching': [
          Exercise(
            name: 'Hamstring Stretch',
            description: 'Basic hamstring flexibility',
            category: 'Flexibility',
            type: 'Stretching',
          ),
          Exercise(
            name: 'Quad Stretch',
            description: 'Quadriceps flexibility exercise',
            category: 'Flexibility',
            type: 'Stretching',
          ),
          Exercise(
            name: 'Shoulder Stretch',
            description: 'Upper body flexibility',
            category: 'Flexibility',
            type: 'Stretching',
          ),
        ],
        'Mobility': [
          Exercise(
            name: 'Hip Circles',
            description: 'Hip mobility and warm-up',
            category: 'Flexibility',
            type: 'Mobility',
          ),
          Exercise(
            name: 'Arm Circles',
            description: 'Shoulder mobility exercise',
            category: 'Flexibility',
            type: 'Mobility',
          ),
        ],
        'Recovery': [
          Exercise(
            name: 'Foam Rolling',
            description: 'Self-myofascial release',
            category: 'Flexibility',
            type: 'Recovery',
          ),
          Exercise(
            name: 'Gentle Stretching',
            description: 'Light recovery stretches',
            category: 'Flexibility',
            type: 'Recovery',
          ),
        ],
      },
    };
  }

  // Legacy method for backward compatibility
  static Map<String, List<Exercise>> getCategoryExercises() {
    Map<String, List<Exercise>> result = {};
    final categoryTypeExercises = getCategoryTypeExercises();

    for (String category in categoryTypeExercises.keys) {
      result[category] = [];
      for (String type in categoryTypeExercises[category]!.keys) {
        result[category]!.addAll(categoryTypeExercises[category]![type]!);
      }
    }

    return result;
  }

  // Get exercises for a specific category and type
  static List<Exercise> getTypeExercises(String category, String type) {
    return getCategoryTypeExercises()[category]?[type] ?? [];
  }
}

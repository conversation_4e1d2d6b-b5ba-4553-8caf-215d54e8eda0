import 'package:flutter/material.dart';
import '../models/goal.dart';
import '../services/database_service.dart';
import '../services/auth_service.dart';

class GoalDebugScreen extends StatefulWidget {
  const GoalDebugScreen({super.key});

  @override
  State<GoalDebugScreen> createState() => _GoalDebugScreenState();
}

class _GoalDebugScreenState extends State<GoalDebugScreen> {
  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();
  String _debugInfo = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _runGoalTests();
  }

  Future<void> _runGoalTests() async {
    setState(() {
      _isLoading = true;
      _debugInfo = 'Running goal system tests...\n\n';
    });

    final user = _authService.currentUser;
    if (user == null) {
      setState(() {
        _debugInfo += '❌ User not authenticated\n';
        _isLoading = false;
      });
      return;
    }

    setState(() {
      _debugInfo += '✅ User authenticated: ${user.uid}\n\n';
    });

    try {
      // Test 1: Create a test goal
      setState(() {
        _debugInfo += 'Test 1: Creating test goal...\n';
      });

      final testGoal = Goal(
        id: '',
        userId: user.uid,
        title: 'Test Goal',
        description: 'This is a test goal created by the debug screen',
        type: 'workout_frequency',
        targetValue: 5.0,
        unit: 'workouts',
        createdAt: DateTime.now(),
        targetDate: DateTime.now().add(const Duration(days: 30)),
      );

      await _databaseService.saveGoal(user.uid, testGoal);
      setState(() {
        _debugInfo += '✅ Test goal created successfully\n\n';
      });

      // Test 2: Load goals
      setState(() {
        _debugInfo += 'Test 2: Loading goals...\n';
      });

      final goalsStream = _databaseService.getActiveGoals(user.uid);
      final goals = await goalsStream.first;

      setState(() {
        _debugInfo += '✅ Loaded ${goals.length} goals\n';
        for (int i = 0; i < goals.length; i++) {
          final goal = goals[i];
          _debugInfo += '  Goal ${i + 1}: ${goal.title}\n';
          _debugInfo += '    Type: ${goal.type}\n';
          _debugInfo += '    Target: ${goal.targetValue} ${goal.unit}\n';
          _debugInfo += '    Progress: ${goal.progressText}\n';
          _debugInfo +=
              '    Target Date: ${goal.targetDate.day}/${goal.targetDate.month}/${goal.targetDate.year}\n';
          _debugInfo += '    Days Remaining: ${goal.daysRemaining}\n\n';
        }
      });

      // Test 3: Test goal queries
      setState(() {
        _debugInfo += 'Test 3: Testing goal queries...\n';
      });

      final allGoalsStream = _databaseService.getGoals(user.uid);
      final allGoals = await allGoalsStream.first;

      final completedGoalsStream = _databaseService.getCompletedGoals(user.uid);
      final completedGoals = await completedGoalsStream.first;

      setState(() {
        _debugInfo += '✅ All goals: ${allGoals.length}\n';
        _debugInfo += '✅ Active goals: ${goals.length}\n';
        _debugInfo += '✅ Completed goals: ${completedGoals.length}\n\n';
      });

      setState(() {
        _debugInfo += '🎉 All tests completed successfully!\n';
      });
    } catch (e) {
      setState(() {
        _debugInfo += '❌ Error during tests: $e\n';
      });
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _createSampleGoals() async {
    final user = _authService.currentUser;
    if (user == null) return;

    setState(() {
      _isLoading = true;
      _debugInfo += '\nCreating sample goals...\n';
    });

    try {
      // Sample goals
      final sampleGoals = [
        Goal(
          id: '',
          userId: user.uid,
          title: 'Weekly Workout Goal',
          description: 'Complete 5 workouts per week',
          type: 'workout_frequency',
          targetValue: 5.0,
          unit: 'workouts',
          createdAt: DateTime.now(),
          targetDate: DateTime.now().add(const Duration(days: 7)),
        ),
        Goal(
          id: '',
          userId: user.uid,
          title: 'Bench Press Goal',
          description: 'Bench press 100kg',
          type: 'strength_goal',
          targetValue: 100.0,
          currentValue: 80.0,
          unit: 'kg',
          createdAt: DateTime.now(),
          targetDate: DateTime.now().add(const Duration(days: 60)),
          exerciseName: 'Bench Press',
        ),
        Goal(
          id: '',
          userId: user.uid,
          title: 'Monthly Cardio',
          description: 'Complete 300 minutes of cardio this month',
          type: 'cardio_goal',
          targetValue: 300.0,
          currentValue: 120.0,
          unit: 'minutes',
          createdAt: DateTime.now(),
          targetDate: DateTime.now().add(const Duration(days: 30)),
        ),
      ];

      for (final goal in sampleGoals) {
        await _databaseService.saveGoal(user.uid, goal);
        setState(() {
          _debugInfo += '✅ Created: ${goal.title}\n';
        });
      }

      setState(() {
        _debugInfo += '\n🎉 Sample goals created successfully!\n';
      });

      // Refresh the test
      await _runGoalTests();
    } catch (e) {
      setState(() {
        _debugInfo += '❌ Error creating sample goals: $e\n';
      });
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Goal Debug'),
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _runGoalTests),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Text(
                      _debugInfo,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _runGoalTests,
                    child: const Text('Run Tests'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _createSampleGoals,
                    child: const Text('Create Samples'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('Close'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

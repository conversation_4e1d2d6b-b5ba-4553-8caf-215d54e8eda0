// This file should be moved to lib/utils/image_helper.dart
import 'package:flutter/material.dart';

class ImageHelper {
  static Widget assetImage({
    required String path,
    required BoxFit fit,
    double? width,
    double? height,
    Color? fallbackColor,
    IconData fallbackIcon = Icons.image,
  }) {
    return Image.asset(
      path,
      fit: fit,
      width: width,
      height: height,
      errorBuilder: (context, error, stackTrace) {
        // Replace print with debugPrint
        debugPrint('Error loading image $path: $error');
        return Container(
          width: width,
          height: height,
          color: fallbackColor ?? Theme.of(context).colorScheme.primary,
          child: Center(
            child: Icon(
              fallbackIcon,
              color: Colors.white,
              size: width != null ? width / 3 : 24,
            ),
          ),
        );
      },
    );
  }
}

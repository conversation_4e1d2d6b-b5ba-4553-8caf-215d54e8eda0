import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/database_service.dart';

class WorkoutsStatsScreen extends StatefulWidget {
  const WorkoutsStatsScreen({super.key});

  @override
  State<WorkoutsStatsScreen> createState() => _WorkoutsStatsScreenState();
}

class _WorkoutsStatsScreenState extends State<WorkoutsStatsScreen> {
  final DatabaseService _databaseService = DatabaseService();
  Map<String, dynamic> _stats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  Future<void> _loadStats() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      try {
        final stats = await _databaseService.getDetailedWorkoutStats(user.uid);
        setState(() {
          _stats = stats;
          _isLoading = false;
        });
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(title: const Text('Workout Statistics')),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : ListView(
                padding: const EdgeInsets.all(16.0),
                children: [
                  _buildSummaryCard(context),
                  const SizedBox(height: 24),
                  Text(
                    'Monthly Breakdown',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  _buildMonthlyChart(context),
                  const SizedBox(height: 24),
                  Text(
                    'Most Frequent Workouts',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  ..._buildWorkoutTypeCards(context, colorScheme),
                ],
              ),
    );
  }

  Widget _buildSummaryCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Total Workouts',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                Text(
                  '${_stats['totalWorkouts'] ?? 0}',
                  style: Theme.of(context).textTheme.displayMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                Text('sessions', style: Theme.of(context).textTheme.bodyLarge),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildStatItem(
                  context,
                  'This Month',
                  '${_stats['thisMonthWorkouts'] ?? 0}',
                ),
                _buildStatItem(
                  context,
                  'Last Month',
                  '${_stats['lastMonthWorkouts'] ?? 0}',
                ),
                _buildStatItem(
                  context,
                  'Average',
                  _stats['averageWorkouts'] ?? '0.0',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildMonthlyChart(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Last 6 Months',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                TextButton(onPressed: () {}, child: const Text('View More')),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: Center(
                child: Text(
                  'Chart will be displayed here',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildWorkoutTypeCards(
    BuildContext context,
    ColorScheme colorScheme,
  ) {
    final workoutTypes = _stats['workoutTypes'] as List<dynamic>? ?? [];

    if (workoutTypes.isEmpty) {
      return [
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Center(
              child: Text(
                'No workout data available yet',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ),
        ),
      ];
    }

    return workoutTypes.map<Widget>((workoutType) {
      final name = workoutType['name'] ?? 'Unknown';
      final count = workoutType['count'] ?? 0;
      return Padding(
        padding: const EdgeInsets.only(bottom: 12.0),
        child: _buildWorkoutTypeCard(
          context,
          name,
          '$count sessions',
          colorScheme.primary,
        ),
      );
    }).toList();
  }

  Widget _buildWorkoutTypeCard(
    BuildContext context,
    String type,
    String count,
    Color color,
  ) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withAlpha(179), // 0.7 * 255 = 178.5 ≈ 179
          child: Icon(Icons.fitness_center, color: color),
        ),
        title: Text(type),
        subtitle: Text(count),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Theme.of(
            context,
          ).colorScheme.onSurface.withAlpha(51), // 0.2 * 255 = 51
        ),
        onTap: () {
          // Navigate to detailed view for this workout type
        },
      ),
    );
  }
}

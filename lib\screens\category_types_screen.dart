import 'package:flutter/material.dart';
import '../data/exercise_data.dart';
import 'type_exercises_screen.dart';

class CategoryTypesScreen extends StatelessWidget {
  final String category;
  final String imagePath;

  const CategoryTypesScreen({
    super.key,
    required this.category,
    required this.imagePath,
  });

  @override
  Widget build(BuildContext context) {
    final types = ExerciseData.getCategoryTypes()[category] ?? [];

    return Scaffold(
      appBar: AppBar(
        title: Text('$category Types'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Category Header with Image
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(imagePath),
                fit: BoxFit.cover,
              ),
            ),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category,
                      style: Theme.of(
                        context,
                      ).textTheme.headlineMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${types.length} types available',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Types List
          Expanded(
            child:
                types.isEmpty
                    ? const Center(
                      child: Text('No types found for this category'),
                    )
                    : Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Choose a Type',
                            style: Theme.of(context).textTheme.titleLarge
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 16),
                          Expanded(
                            child: GridView.builder(
                              gridDelegate:
                                  const SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 2,
                                    mainAxisSpacing: 16,
                                    crossAxisSpacing: 16,
                                    childAspectRatio: 1.2,
                                  ),
                              itemCount: types.length,
                              itemBuilder: (context, index) {
                                final type = types[index];
                                final exerciseCount =
                                    ExerciseData.getTypeExercises(
                                      category,
                                      type,
                                    ).length;

                                return _buildTypeCard(
                                  context,
                                  type,
                                  exerciseCount,
                                  _getTypeIcon(type),
                                  _getTypeColor(index),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeCard(
    BuildContext context,
    String type,
    int exerciseCount,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => TypeExercisesScreen(
                    category: category,
                    type: type,
                    exercises: ExerciseData.getTypeExercises(category, type),
                  ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 32, color: color),
              ),
              const SizedBox(height: 12),
              Text(
                type,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                '$exerciseCount exercises',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'upper body':
        return Icons.fitness_center;
      case 'lower body':
        return Icons.directions_run;
      case 'core':
        return Icons.center_focus_strong;
      case 'full body':
        return Icons.accessibility_new;
      case 'beginner hiit':
        return Icons.play_arrow;
      case 'intermediate hiit':
        return Icons.fast_forward;
      case 'advanced hiit':
        return Icons.flash_on;
      case 'tabata':
        return Icons.timer;
      case 'low impact':
        return Icons.trending_down;
      case 'high impact':
        return Icons.trending_up;
      case 'endurance':
        return Icons.schedule;
      case 'sprint':
        return Icons.speed;
      case 'yoga':
        return Icons.self_improvement;
      case 'stretching':
        return Icons.accessibility;
      case 'mobility':
        return Icons.rotate_right;
      case 'recovery':
        return Icons.healing;
      default:
        return Icons.fitness_center;
    }
  }

  Color _getTypeColor(int index) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];
    return colors[index % colors.length];
  }
}

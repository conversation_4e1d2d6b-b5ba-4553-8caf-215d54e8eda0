import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/auth_service.dart';
import '../services/database_service.dart';
import 'goal_debug_screen.dart';

class DebugScreen extends StatefulWidget {
  const DebugScreen({super.key});

  @override
  State<DebugScreen> createState() => _DebugScreenState();
}

class _DebugScreenState extends State<DebugScreen> {
  final AuthService _authService = AuthService();
  final DatabaseService _databaseService = DatabaseService();
  String _debugInfo = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkAuthAndFirestore();
  }

  Future<void> _checkAuthAndFirestore() async {
    setState(() {
      _isLoading = true;
      _debugInfo = 'Checking authentication and Firestore connection...\n\n';
    });

    try {
      // Check authentication
      final user = _authService.currentUser;
      if (user != null) {
        setState(() {
          _debugInfo += '✅ User authenticated\n';
          _debugInfo += 'UID: ${user.uid}\n';
          _debugInfo += 'Email: ${user.email}\n';
          _debugInfo += 'Display Name: ${user.displayName}\n\n';
        });

        // Test Firestore connection
        setState(() {
          _debugInfo += 'Testing Firestore connection...\n';
        });

        // Try to read user profile
        try {
          final profile = await _databaseService.getUserProfile(user.uid);
          setState(() {
            _debugInfo += '✅ Firestore read successful\n';
            _debugInfo += 'Profile exists: ${profile != null}\n\n';
          });
        } catch (e) {
          setState(() {
            _debugInfo += '❌ Firestore read failed: $e\n\n';
          });
        }

        // Test Firestore write
        setState(() {
          _debugInfo += 'Testing Firestore write...\n';
        });

        try {
          await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .set({
                'testField': 'test_value',
                'timestamp': FieldValue.serverTimestamp(),
              }, SetOptions(merge: true));

          setState(() {
            _debugInfo += '✅ Firestore write successful\n\n';
          });
        } catch (e) {
          setState(() {
            _debugInfo += '❌ Firestore write failed: $e\n\n';
          });
        }
      } else {
        setState(() {
          _debugInfo += '❌ User not authenticated\n\n';
        });
      }
    } catch (e) {
      setState(() {
        _debugInfo += '❌ Error during check: $e\n\n';
      });
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Info'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _checkAuthAndFirestore,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Text(
                      _debugInfo,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _checkAuthAndFirestore,
                    child: const Text('Refresh Check'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const GoalDebugScreen(),
                        ),
                      );
                    },
                    child: const Text('Goal Debug'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('Close'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

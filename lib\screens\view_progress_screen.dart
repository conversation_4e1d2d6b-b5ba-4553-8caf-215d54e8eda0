import 'package:flutter/material.dart';
import '../services/database_service.dart';
import '../services/auth_service.dart';
import '../models/workout.dart';
import '../models/user_profile.dart';

class ViewProgressScreen extends StatefulWidget {
  const ViewProgressScreen({super.key});

  @override
  State<ViewProgressScreen> createState() => _ViewProgressScreenState();
}

class _ViewProgressScreenState extends State<ViewProgressScreen> {
  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();

  bool _isLoading = true;
  UserProfile? _userProfile;
  List<Workout> _workouts = [];
  Map<String, dynamic> _progressData = {};
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadProgressData();
  }

  Future<void> _loadProgressData() async {
    final user = _authService.currentUser;
    if (user == null) {
      setState(() {
        _errorMessage = 'User not logged in';
        _isLoading = false;
      });
      return;
    }

    try {
      // Load user profile
      final profile = await _databaseService.getUserProfileObject(user.uid);

      // Load workouts
      final workoutsStream = _databaseService.getWorkouts(user.uid);
      final workouts = await workoutsStream.first;

      // Calculate progress data
      final progressData = await _calculateProgressData(user.uid, workouts);

      setState(() {
        _userProfile = profile;
        _workouts = workouts;
        _progressData = progressData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading progress data: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('View Progress'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _errorMessage = null;
                });
                _loadProgressData();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_workouts.isEmpty) {
      return _buildNoDataView();
    }

    return _buildProgressView();
  }

  Future<Map<String, dynamic>> _calculateProgressData(
    String uid,
    List<Workout> workouts,
  ) async {
    if (workouts.isEmpty) {
      return {};
    }

    // Calculate weekly workouts
    DateTime now = DateTime.now();
    DateTime startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    int weeklyWorkouts =
        workouts.where((w) => w.date.isAfter(startOfWeek)).length;

    // Calculate monthly workouts
    DateTime startOfMonth = DateTime(now.year, now.month, 1);
    int monthlyWorkouts =
        workouts.where((w) => w.date.isAfter(startOfMonth)).length;

    // Calculate exercise progress (weight progression)
    Map<String, List<double>> exerciseWeights = {};
    for (var workout in workouts) {
      for (var exercise in workout.exercises) {
        if (!exerciseWeights.containsKey(exercise.name)) {
          exerciseWeights[exercise.name] = [];
        }
        exerciseWeights[exercise.name]!.add(exercise.weight);
      }
    }

    // Get top 3 exercises by frequency
    Map<String, int> exerciseFrequency = {};
    for (var workout in workouts) {
      for (var exercise in workout.exercises) {
        exerciseFrequency[exercise.name] =
            (exerciseFrequency[exercise.name] ?? 0) + 1;
      }
    }

    var sortedExercises =
        exerciseFrequency.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    List<Map<String, dynamic>> topExercises = [];
    for (int i = 0; i < 3 && i < sortedExercises.length; i++) {
      String exerciseName = sortedExercises[i].key;
      List<double> weights = exerciseWeights[exerciseName] ?? [];
      if (weights.isNotEmpty) {
        double minWeight = weights.reduce((a, b) => a < b ? a : b);
        double maxWeight = weights.reduce((a, b) => a > b ? a : b);
        double progress =
            minWeight > 0 ? (maxWeight - minWeight) / minWeight : 0;

        topExercises.add({
          'name': exerciseName,
          'minWeight': minWeight,
          'maxWeight': maxWeight,
          'progress': progress.clamp(0, 1),
          'progressText':
              '${minWeight.toStringAsFixed(1)}kg → ${maxWeight.toStringAsFixed(1)}kg',
        });
      }
    }

    return {
      'weeklyWorkouts': weeklyWorkouts,
      'monthlyWorkouts': monthlyWorkouts,
      'topExercises': topExercises,
      'totalWorkouts': workouts.length,
    };
  }

  Widget _buildNoDataView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 80,
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 24),
          Text(
            'No Progress Data Yet',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Text(
            'Start logging workouts to see your progress here!',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: const Icon(Icons.add),
            label: const Text('Log Your First Workout'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressView() {
    final weeklyWorkouts = _progressData['weeklyWorkouts'] ?? 0;
    final monthlyWorkouts = _progressData['monthlyWorkouts'] ?? 0;
    final totalWorkouts = _progressData['totalWorkouts'] ?? 0;
    final topExercises =
        _progressData['topExercises'] as List<Map<String, dynamic>>? ?? [];

    // Calculate weekly goal progress
    final weeklyGoal = _userProfile?.weeklyWorkoutGoal ?? 5;
    final weeklyProgress =
        weeklyGoal > 0 ? (weeklyWorkouts / weeklyGoal).clamp(0.0, 1.0) : 0.0;

    // Calculate monthly goal progress (assume 20 workouts per month as default)
    const monthlyGoal = 20;
    final monthlyProgress = (monthlyWorkouts / monthlyGoal).clamp(0.0, 1.0);

    return RefreshIndicator(
      onRefresh: _loadProgressData,
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildProgressCard(
            context,
            'Weekly Workouts',
            '$weeklyWorkouts/$weeklyGoal',
            Icons.calendar_today,
            Colors.blue,
            weeklyProgress,
          ),
          const SizedBox(height: 16),
          _buildProgressCard(
            context,
            'Monthly Workouts',
            '$monthlyWorkouts/$monthlyGoal',
            Icons.fitness_center,
            Colors.green,
            monthlyProgress,
          ),
          const SizedBox(height: 16),
          _buildProgressCard(
            context,
            'Total Workouts',
            '$totalWorkouts',
            Icons.emoji_events,
            Colors.orange,
            1.0, // Always full for total count
          ),
          if (topExercises.isNotEmpty) ...[
            const SizedBox(height: 24),
            Text(
              'Exercise Progress',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...topExercises.map(
              (exercise) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildStrengthCard(
                  context,
                  exercise['name'],
                  exercise['progressText'],
                  exercise['progress'],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
    double progress,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: color.withAlpha(
                51,
              ), // Changed from withOpacity(0.2)
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 10,
              borderRadius: BorderRadius.circular(5),
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStrengthCard(
    BuildContext context,
    String exercise,
    String progress,
    double percentage,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    exercise,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    progress,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            CircularProgressIndicator(
              value: percentage,
              backgroundColor: Colors.grey.withAlpha(
                51,
              ), // Changed from withOpacity(0.2)
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

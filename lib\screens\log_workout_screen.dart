import 'package:flutter/material.dart';
import '../models/exercise.dart';
import '../models/workout.dart';
import '../services/database_service.dart';
import '../services/auth_service.dart';

class LogWorkoutScreen extends StatefulWidget {
  const LogWorkoutScreen({super.key});

  @override
  State<LogWorkoutScreen> createState() => _LogWorkoutScreenState();
}

class _LogWorkoutScreenState extends State<LogWorkoutScreen> {
  final _formKey = GlobalKey<FormState>();
  final _workoutNameController = TextEditingController();
  final List<Exercise> _exercises = [];
  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();
  bool _isSaving = false;
  String? _selectedCategory;

  final List<String> _categories = [
    'Strength',
    'HIIT',
    'Cardio',
    'Flexibility',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Log Workout'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextFormField(
                controller: _workoutNameController,
                decoration: const InputDecoration(
                  labelText: 'Workout Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a workout name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(
                  labelText: 'Workout Category',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.category),
                ),
                items:
                    _categories.map((String category) {
                      return DropdownMenuItem<String>(
                        value: category,
                        child: Text(category),
                      );
                    }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedCategory = newValue;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a workout category';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Exercises',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  ElevatedButton.icon(
                    onPressed: _addExercise,
                    icon: const Icon(Icons.add),
                    label: const Text('Add Exercise'),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Expanded(
                child:
                    _exercises.isEmpty
                        ? Center(
                          child: Text(
                            'No exercises added yet',
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                        )
                        : ListView.builder(
                          itemCount: _exercises.length,
                          itemBuilder: (context, index) {
                            return ExerciseCard(
                              exercise: _exercises[index],
                              onDelete: () => _removeExercise(index),
                            );
                          },
                        ),
              ),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _isSaving ? null : _saveWorkout,
                  child:
                      _isSaving
                          ? const CircularProgressIndicator()
                          : const Text('Save Workout'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _addExercise() {
    showDialog(
      context: context,
      builder:
          (context) => AddExerciseDialog(
            onAdd: (exercise) {
              setState(() {
                _exercises.add(exercise);
              });
            },
          ),
    );
  }

  void _removeExercise(int index) {
    setState(() {
      _exercises.removeAt(index);
    });
  }

  Future<void> _saveWorkout() async {
    if (_formKey.currentState!.validate() && _exercises.isNotEmpty) {
      final user = _authService.currentUser;
      if (user == null) return;

      setState(() {
        _isSaving = true;
      });

      try {
        // Create workout object
        final workout = Workout(
          id: '', // Will be generated by Firestore
          name: _workoutNameController.text,
          date: DateTime.now(),
          exercises: _exercises,
          category: _selectedCategory,
        );

        try {
          // Save workout to database
          await _databaseService.saveWorkout(user.uid, workout);

          // Update user stats
          double totalWeight = _exercises.fold(
            0.0,
            (total, exercise) =>
                total + (exercise.weight * exercise.sets * exercise.reps),
          );

          await _databaseService.updateUserStats(
            user.uid,
            additionalWorkouts: 1,
            additionalWeight: totalWeight,
            additionalExercises: _exercises.length,
          );

          // Update goal progress
          await _databaseService.updateGoalsAfterWorkout(user.uid, workout);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Workout saved successfully!'),
                backgroundColor: Colors.green,
              ),
            );

            // Clear form
            _workoutNameController.clear();
            setState(() {
              _exercises.clear();
              _selectedCategory = null;
            });
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error saving workout: $e'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 5),
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('Error saving workout: $e')));
        }
      } finally {
        if (mounted) {
          setState(() {
            _isSaving = false;
          });
        }
      }
    } else if (_exercises.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one exercise')),
      );
    }
  }
}

class ExerciseCard extends StatelessWidget {
  final Exercise exercise;
  final VoidCallback onDelete;

  const ExerciseCard({
    super.key,
    required this.exercise,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 10),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  exercise.name,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: onDelete,
                ),
              ],
            ),
            Text('Sets: ${exercise.sets}, Reps: ${exercise.reps}'),
            if (exercise.weight > 0) Text('Weight: ${exercise.weight} kg'),
          ],
        ),
      ),
    );
  }
}

class AddExerciseDialog extends StatefulWidget {
  final Function(Exercise) onAdd;

  const AddExerciseDialog({super.key, required this.onAdd});

  @override
  State<AddExerciseDialog> createState() => _AddExerciseDialogState();
}

class _AddExerciseDialogState extends State<AddExerciseDialog> {
  final _nameController = TextEditingController();
  final _setsController = TextEditingController();
  final _repsController = TextEditingController();
  final _weightController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Exercise'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(labelText: 'Exercise Name'),
            ),
            TextField(
              controller: _setsController,
              decoration: const InputDecoration(labelText: 'Sets'),
              keyboardType: TextInputType.number,
            ),
            TextField(
              controller: _repsController,
              decoration: const InputDecoration(labelText: 'Reps'),
              keyboardType: TextInputType.number,
            ),
            TextField(
              controller: _weightController,
              decoration: const InputDecoration(labelText: 'Weight (kg)'),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            final exercise = Exercise(
              name: _nameController.text,
              sets: int.tryParse(_setsController.text) ?? 0,
              reps: int.tryParse(_repsController.text) ?? 0,
              weight: double.tryParse(_weightController.text) ?? 0,
            );
            widget.onAdd(exercise);
            Navigator.pop(context);
          },
          child: const Text('Add'),
        ),
      ],
    );
  }
}

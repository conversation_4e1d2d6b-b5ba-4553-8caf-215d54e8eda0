import 'exercise.dart';

class Workout {
  final String id;
  final String name;
  final DateTime date;
  final List<Exercise> exercises;
  final String? category; // Workout category for image selection
  final String? notes; // Optional workout notes

  Workout({
    required this.id,
    required this.name,
    required this.date,
    required this.exercises,
    this.category,
    this.notes,
  });

  // Get image path based on category
  String get imagePath {
    switch (category?.toLowerCase()) {
      case 'strength':
        return 'assets/images/strength.jpg';
      case 'hiit':
        return 'assets/images/hiit.jpg';
      case 'cardio':
        return 'assets/images/cardio.jpg';
      case 'flexibility':
        return 'assets/images/flexibility.jpg';
      default:
        return 'assets/images/strength.jpg'; // Use strength as default
    }
  }

  // Convert to map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'date': date,
      'category': category,
      'notes': notes,
      'exercises':
          exercises
              .map(
                (exercise) => {
                  'name': exercise.name,
                  'sets': exercise.sets,
                  'reps': exercise.reps,
                  'weight': exercise.weight,
                  'duration': exercise.duration,
                },
              )
              .toList(),
    };
  }

  // Create from map (Firestore)
  factory Workout.fromMap(String id, Map<String, dynamic> map) {
    return Workout(
      id: id,
      name: map['name'] ?? '',
      date: map['date']?.toDate() ?? DateTime.now(),
      category: map['category'],
      notes: map['notes'],
      exercises:
          (map['exercises'] as List? ?? [])
              .map(
                (exerciseData) => Exercise(
                  name: exerciseData['name'] ?? '',
                  sets: exerciseData['sets'] ?? 0,
                  reps: exerciseData['reps'] ?? 0,
                  weight: (exerciseData['weight'] ?? 0.0).toDouble(),
                  duration: exerciseData['duration'],
                ),
              )
              .toList(),
    );
  }

  // Copy with method for updates
  Workout copyWith({
    String? id,
    String? name,
    DateTime? date,
    List<Exercise>? exercises,
    String? category,
    String? notes,
  }) {
    return Workout(
      id: id ?? this.id,
      name: name ?? this.name,
      date: date ?? this.date,
      exercises: exercises ?? this.exercises,
      category: category ?? this.category,
      notes: notes ?? this.notes,
    );
  }
}

import 'package:flutter/material.dart';
import '../models/goal.dart';
import '../services/database_service.dart';
import '../services/auth_service.dart';

class SetGoalScreen extends StatefulWidget {
  const SetGoalScreen({super.key});

  @override
  State<SetGoalScreen> createState() => _SetGoalScreenState();
}

class _SetGoalScreenState extends State<SetGoalScreen> {
  DateTime goalDate = DateTime.now().add(const Duration(days: 30));
  String _selectedGoalType = 'Workout Frequency';
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _targetValueController = TextEditingController();
  final _exerciseNameController = TextEditingController();
  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();
  bool _isSaving = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Set New Goal')),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            Text(
              'Create a New Goal',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 24),

            // Goal Type Selector
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Goal Type',
                prefixIcon: Icon(Icons.category),
              ),
              value: _selectedGoalType,
              items:
                  [
                    'Workout Frequency',
                    'Weight Goal',
                    'Strength Goal',
                    'Cardio Goal',
                  ].map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
              onChanged: (newValue) {
                setState(() {
                  _selectedGoalType = newValue!;
                });
              },
            ),

            const SizedBox(height: 16),

            // Goal Title
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Goal Title',
                prefixIcon: Icon(Icons.flag),
                hintText: 'e.g., Weekly Workout Goal',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a goal title';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Goal Description
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Goal Description',
                prefixIcon: Icon(Icons.description),
                hintText: 'e.g., Complete 5 workouts per week',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a goal description';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Target Value
            TextFormField(
              controller: _targetValueController,
              decoration: const InputDecoration(
                labelText: 'Target Value',
                prefixIcon: Icon(Icons.track_changes),
                hintText: 'e.g., 5',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a target value';
                }
                if (double.tryParse(value) == null) {
                  return 'Please enter a valid number';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Exercise Name (for strength goals)
            if (_selectedGoalType == 'Strength Goal') ...[
              TextFormField(
                controller: _exerciseNameController,
                decoration: const InputDecoration(
                  labelText: 'Exercise Name',
                  prefixIcon: Icon(Icons.fitness_center),
                  hintText: 'e.g., Bench Press',
                ),
                validator: (value) {
                  if (_selectedGoalType == 'Strength Goal' &&
                      (value == null || value.isEmpty)) {
                    return 'Please enter an exercise name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
            ],

            const SizedBox(height: 16),

            // Goal Deadline
            TextFormField(
              decoration: InputDecoration(
                labelText: 'Target Date',
                prefixIcon: const Icon(Icons.calendar_today),
                hintText: 'Select target date',
              ),
              readOnly: true,
              controller: TextEditingController(
                text: '${goalDate.day}/${goalDate.month}/${goalDate.year}',
              ),
              validator: (value) {
                if (goalDate.isBefore(
                  DateTime.now().subtract(const Duration(days: 1)),
                )) {
                  return 'Target date must be in the future';
                }
                return null;
              },
              onTap: () async {
                final DateTime? selectedDate = await showDatePicker(
                  context: context,
                  initialDate: goalDate,
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );

                if (selectedDate != null) {
                  setState(() {
                    goalDate = selectedDate;
                  });
                }
              },
            ),

            const SizedBox(height: 24),

            // Submit Button
            ElevatedButton(
              onPressed: _isSaving ? null : _saveGoal,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child:
                  _isSaving
                      ? const CircularProgressIndicator()
                      : const Text('Save Goal'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveGoal() async {
    if (!_formKey.currentState!.validate()) return;

    final user = _authService.currentUser;
    if (user == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('User not logged in')));
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // Determine goal type and unit
      String goalType;
      String unit;

      switch (_selectedGoalType) {
        case 'Workout Frequency':
          goalType = 'workout_frequency';
          unit = 'workouts';
          break;
        case 'Weight Goal':
          goalType = 'weight_goal';
          unit = 'kg';
          break;
        case 'Strength Goal':
          goalType = 'strength_goal';
          unit = 'kg';
          break;
        case 'Cardio Goal':
          goalType = 'cardio_goal';
          unit = 'minutes';
          break;
        default:
          goalType = 'workout_frequency';
          unit = 'workouts';
      }

      // Create goal object
      final goal = Goal(
        id: '', // Will be generated by Firestore
        userId: user.uid,
        title: _titleController.text,
        description: _descriptionController.text,
        type: goalType,
        targetValue: double.parse(_targetValueController.text),
        unit: unit,
        createdAt: DateTime.now(),
        targetDate: goalDate,
        exerciseName:
            _selectedGoalType == 'Strength Goal'
                ? _exerciseNameController.text
                : null,
      );

      // Save goal to database
      await _databaseService.saveGoal(user.uid, goal);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Goal saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving goal: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}

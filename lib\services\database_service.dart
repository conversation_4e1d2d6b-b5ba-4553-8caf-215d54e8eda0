import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/workout.dart';
import '../models/user_profile.dart';
import '../models/goal.dart';

class DatabaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection references
  CollectionReference get users => _firestore.collection('users');

  // Get user document reference
  DocumentReference userDoc(String uid) => users.doc(uid);

  // Get workouts collection for a user
  CollectionReference workoutsCollection(String uid) =>
      userDoc(uid).collection('workouts');

  // Get goals collection for a user
  CollectionReference goalsCollection(String uid) =>
      userDoc(uid).collection('goals');

  // Save workout
  Future<void> saveWorkout(String uid, Workout workout) async {
    try {
      // Check if user is authenticated
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null || currentUser.uid != uid) {
        throw Exception('User not authenticated or UID mismatch');
      }

      // Use the workout's toMap method
      Map<String, dynamic> workoutData = workout.toMap();

      // If workout has an ID, update it, otherwise add a new one
      if (workout.id.isNotEmpty) {
        await workoutsCollection(uid).doc(workout.id).update(workoutData);
      } else {
        await workoutsCollection(uid).add(workoutData);
      }
    } catch (e) {
      rethrow; // Re-throw the error so the UI can handle it
    }
  }

  // Get all workouts for a user
  Stream<List<Workout>> getWorkouts(String uid) {
    return workoutsCollection(
      uid,
    ).orderBy('date', descending: true).snapshots().map((snapshot) {
      return snapshot.docs.map((doc) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        return Workout.fromMap(doc.id, data);
      }).toList();
    });
  }

  // Delete a workout
  Future<void> deleteWorkout(String uid, String workoutId) async {
    await workoutsCollection(uid).doc(workoutId).delete();
  }

  // Update user profile
  Future<void> updateUserProfile(String uid, Map<String, dynamic> data) async {
    await userDoc(uid).set(data, SetOptions(merge: true));
  }

  // Get user profile
  Future<Map<String, dynamic>?> getUserProfile(String uid) async {
    DocumentSnapshot doc = await userDoc(uid).get();
    if (doc.exists) {
      return doc.data() as Map<String, dynamic>;
    }
    return null;
  }

  // Create or update user profile
  Future<void> createOrUpdateUserProfile(UserProfile profile) async {
    await userDoc(profile.uid).set(profile.toMap(), SetOptions(merge: true));
  }

  // Get user profile as UserProfile object
  Future<UserProfile?> getUserProfileObject(String uid) async {
    DocumentSnapshot doc = await userDoc(uid).get();
    if (doc.exists) {
      return UserProfile.fromMap(doc.data() as Map<String, dynamic>);
    }
    return null;
  }

  // Update user stats after workout
  Future<void> updateUserStats(
    String uid, {
    int? additionalWorkouts,
    double? additionalWeight,
    int? additionalExercises,
  }) async {
    DocumentReference userDocRef = userDoc(uid);

    await _firestore.runTransaction((transaction) async {
      DocumentSnapshot snapshot = await transaction.get(userDocRef);

      if (snapshot.exists) {
        Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;

        // Update stats
        int currentWorkouts = data['totalWorkouts'] ?? 0;
        double currentWeight = (data['totalWeightLifted'] ?? 0.0).toDouble();
        int currentExercises = data['totalExercises'] ?? 0;

        Map<String, dynamic> updates = {
          'lastLoginAt': FieldValue.serverTimestamp(),
        };

        if (additionalWorkouts != null) {
          updates['totalWorkouts'] = currentWorkouts + additionalWorkouts;
        }

        if (additionalWeight != null) {
          updates['totalWeightLifted'] = currentWeight + additionalWeight;
        }

        if (additionalExercises != null) {
          updates['totalExercises'] = currentExercises + additionalExercises;
        }

        transaction.update(userDocRef, updates);
      }
    });
  }

  // Calculate and update user streak based on workout dates
  Future<void> calculateAndUpdateStreak(String uid) async {
    try {
      // Get all workouts ordered by date
      final workoutsSnapshot =
          await workoutsCollection(uid).orderBy('date', descending: true).get();

      if (workoutsSnapshot.docs.isEmpty) {
        await updateUserStreak(uid, 0);
        return;
      }

      List<DateTime> workoutDates =
          workoutsSnapshot.docs
              .map((doc) {
                final data = doc.data() as Map<String, dynamic>;
                return (data['date'] as Timestamp).toDate();
              })
              .map((date) => DateTime(date.year, date.month, date.day))
              .toSet()
              .toList();

      workoutDates.sort((a, b) => b.compareTo(a)); // Most recent first

      int currentStreak = 0;
      DateTime today = DateTime.now();
      DateTime checkDate = DateTime(today.year, today.month, today.day);

      // Check if there's a workout today or yesterday to start the streak
      bool hasRecentWorkout = workoutDates.any(
        (date) =>
            date.isAtSameMomentAs(checkDate) ||
            date.isAtSameMomentAs(checkDate.subtract(const Duration(days: 1))),
      );

      if (!hasRecentWorkout) {
        await updateUserStreak(uid, 0);
        return;
      }

      // Calculate current streak
      for (DateTime workoutDate in workoutDates) {
        if (workoutDate.isAtSameMomentAs(checkDate)) {
          currentStreak++;
          checkDate = checkDate.subtract(const Duration(days: 1));
        } else if (workoutDate.isBefore(checkDate)) {
          // Gap in streak, stop counting
          break;
        }
      }

      await updateUserStreak(uid, currentStreak);
    } catch (e) {
      // Error calculating streak, set to 0
      await updateUserStreak(uid, 0);
    }
  }

  // Update user streak
  Future<void> updateUserStreak(String uid, int newStreak) async {
    DocumentReference userDocRef = userDoc(uid);

    await _firestore.runTransaction((transaction) async {
      DocumentSnapshot snapshot = await transaction.get(userDocRef);

      if (snapshot.exists) {
        Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
        int longestStreak = data['longestStreak'] ?? 0;

        Map<String, dynamic> updates = {
          'currentStreak': newStreak,
          'lastLoginAt': FieldValue.serverTimestamp(),
        };

        // Update longest streak if current is higher
        if (newStreak > longestStreak) {
          updates['longestStreak'] = newStreak;
        }

        transaction.update(userDocRef, updates);
      }
    });
  }

  // Initialize user profile on first login
  Future<void> initializeUserProfile(User user) async {
    UserProfile profile = UserProfile(
      uid: user.uid,
      email: user.email ?? '',
      displayName: user.displayName,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
    );

    // Only create if doesn't exist
    DocumentSnapshot doc = await userDoc(user.uid).get();
    if (!doc.exists) {
      await createOrUpdateUserProfile(profile);
    } else {
      // Update last login time
      await userDoc(user.uid).update({
        'lastLoginAt': FieldValue.serverTimestamp(),
        'displayName': user.displayName, // Update display name if changed
      });
    }
  }

  // Get user stats for dashboard
  Future<Map<String, dynamic>> getUserStats(String uid) async {
    UserProfile? profile = await getUserProfileObject(uid);
    if (profile == null) return {};

    // Get recent workouts count (this week)
    DateTime now = DateTime.now();
    DateTime startOfWeek = now.subtract(Duration(days: now.weekday - 1));

    QuerySnapshot weeklyWorkouts =
        await workoutsCollection(uid)
            .where(
              'date',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startOfWeek),
            )
            .get();

    // Calculate goal progress from active goals
    double goalProgress = await _calculateOverallGoalProgress(uid);

    return {
      'totalWorkouts': profile.totalWorkouts,
      'weeklyWorkouts': weeklyWorkouts.docs.length,
      'currentStreak': profile.currentStreak,
      'totalWeightLifted': profile.totalWeightLifted,
      'goalProgress': goalProgress,
    };
  }

  // Calculate overall goal progress for quick stats
  Future<double> _calculateOverallGoalProgress(String uid) async {
    try {
      // Get all goals and filter in memory to avoid index requirement
      final allGoalsSnapshot = await goalsCollection(uid).get();

      if (allGoalsSnapshot.docs.isEmpty) {
        return 0.0; // No goals at all
      }

      double totalProgress = 0.0;
      int activeGoalCount = 0;

      for (var goalDoc in allGoalsSnapshot.docs) {
        final data = goalDoc.data() as Map<String, dynamic>;
        final isCompleted = data['isCompleted'] ?? false;

        // Only process active goals (not completed)
        if (!isCompleted) {
          final goal = Goal.fromMap(goalDoc.id, data);
          totalProgress += goal.progressPercentage * 100;
          activeGoalCount++;
        }
      }

      return activeGoalCount > 0
          ? (totalProgress / activeGoalCount).clamp(0, 100)
          : 0.0;
    } catch (e) {
      return 0.0; // Return 0 if there's an error
    }
  }

  // Get detailed streak statistics
  Future<Map<String, dynamic>> getStreakStats(String uid) async {
    try {
      final profile = await getUserProfileObject(uid);
      if (profile == null) return {};

      // Get workouts for this month
      DateTime now = DateTime.now();
      DateTime startOfMonth = DateTime(now.year, now.month, 1);

      QuerySnapshot monthlyWorkouts =
          await workoutsCollection(uid)
              .where(
                'date',
                isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth),
              )
              .get();

      // Get workouts for this week
      DateTime startOfWeek = now.subtract(Duration(days: now.weekday - 1));
      QuerySnapshot weeklyWorkouts =
          await workoutsCollection(uid)
              .where(
                'date',
                isGreaterThanOrEqualTo: Timestamp.fromDate(startOfWeek),
              )
              .get();

      return {
        'currentStreak': profile.currentStreak,
        'longestStreak': profile.longestStreak,
        'totalWorkouts': profile.totalWorkouts,
        'monthlyWorkouts': monthlyWorkouts.docs.length,
        'weeklyWorkouts': weeklyWorkouts.docs.length,
      };
    } catch (e) {
      return {};
    }
  }

  // ===== GOAL MANAGEMENT METHODS =====

  // Save goal
  Future<void> saveGoal(String uid, Goal goal) async {
    try {
      // Check if user is authenticated
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null || currentUser.uid != uid) {
        throw Exception('User not authenticated or UID mismatch');
      }

      Map<String, dynamic> goalData = goal.toMap();

      if (goal.id.isNotEmpty) {
        await goalsCollection(uid).doc(goal.id).update(goalData);
      } else {
        await goalsCollection(uid).add(goalData);
      }
    } catch (e) {
      rethrow; // Re-throw the error so the UI can handle it
    }
  }

  // Get all goals for a user
  Stream<List<Goal>> getGoals(String uid) {
    return goalsCollection(
      uid,
    ).orderBy('createdAt', descending: true).snapshots().map((snapshot) {
      return snapshot.docs.map((doc) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        return Goal.fromMap(doc.id, data);
      }).toList();
    });
  }

  // Get active goals for a user
  Stream<List<Goal>> getActiveGoals(String uid) {
    return goalsCollection(uid).snapshots().map((snapshot) {
      List<Goal> activeGoals = [];
      for (var doc in snapshot.docs) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        final isCompleted = data['isCompleted'] ?? false;
        if (!isCompleted) {
          activeGoals.add(Goal.fromMap(doc.id, data));
        }
      }
      // Sort by target date
      activeGoals.sort((a, b) => a.targetDate.compareTo(b.targetDate));
      return activeGoals;
    });
  }

  // Get completed goals for a user
  Stream<List<Goal>> getCompletedGoals(String uid) {
    return goalsCollection(uid).snapshots().map((snapshot) {
      List<Goal> completedGoals = [];
      for (var doc in snapshot.docs) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        final isCompleted = data['isCompleted'] ?? false;
        if (isCompleted) {
          completedGoals.add(Goal.fromMap(doc.id, data));
        }
      }
      // Sort by target date (most recent first)
      completedGoals.sort((a, b) => b.targetDate.compareTo(a.targetDate));
      return completedGoals;
    });
  }

  // Update goal progress
  Future<void> updateGoalProgress(
    String uid,
    String goalId,
    double newProgress,
  ) async {
    await goalsCollection(uid).doc(goalId).update({
      'currentValue': newProgress,
      'isCompleted':
          newProgress >=
          await goalsCollection(uid).doc(goalId).get().then((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return data['targetValue'] ?? 0.0;
          }),
    });
  }

  // Mark goal as completed
  Future<void> completeGoal(String uid, String goalId) async {
    await goalsCollection(uid).doc(goalId).update({'isCompleted': true});
  }

  // Delete goal
  Future<void> deleteGoal(String uid, String goalId) async {
    await goalsCollection(uid).doc(goalId).delete();
  }

  // Update goal progress based on workout data
  Future<void> updateGoalsAfterWorkout(String uid, Workout workout) async {
    // Get all goals and filter active ones in memory
    final goalsSnapshot = await goalsCollection(uid).get();

    for (var goalDoc in goalsSnapshot.docs) {
      final data = goalDoc.data() as Map<String, dynamic>;
      final isCompleted = data['isCompleted'] ?? false;

      // Skip completed goals
      if (isCompleted) continue;

      final goal = Goal.fromMap(goalDoc.id, data);
      double newProgress = goal.currentValue;

      switch (goal.type) {
        case 'workout_frequency':
          // Increment workout count
          newProgress += 1;
          break;
        case 'strength_goal':
          // Check if workout contains the target exercise
          if (goal.exerciseName != null) {
            for (var exercise in workout.exercises) {
              if (exercise.name.toLowerCase() ==
                  goal.exerciseName!.toLowerCase()) {
                if (exercise.weight > goal.currentValue) {
                  newProgress = exercise.weight;
                }
              }
            }
          }
          break;
        case 'weight_goal':
          // Update total weight lifted
          double workoutWeight = workout.exercises.fold(
            0.0,
            (total, exercise) =>
                total + (exercise.weight * exercise.sets * exercise.reps),
          );
          newProgress += workoutWeight;
          break;
      }

      // Update if progress changed
      if (newProgress != goal.currentValue) {
        bool isCompleted = newProgress >= goal.targetValue;
        await goalsCollection(uid).doc(goalDoc.id).update({
          'currentValue': newProgress,
          'isCompleted': isCompleted,
        });
      }
    }
  }
}

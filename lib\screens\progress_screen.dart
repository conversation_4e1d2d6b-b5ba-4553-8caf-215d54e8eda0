import 'package:flutter/material.dart';

class ProgressScreen extends StatefulWidget {
  const ProgressScreen({super.key});

  @override
  State<ProgressScreen> createState() => _ProgressScreenState();
}

class _ProgressScreenState extends State<ProgressScreen> {
  String _selectedExercise = 'Bench Press';
  String _selectedMetric = 'Weight';

  // Sample data - in a real app, this would come from a database
  final List<String> _exercises = [
    'Bench Press',
    'Squats',
    'Deadlifts',
    'Pull-ups',
  ];

  final List<String> _metrics = ['Weight', 'Reps', 'Sets', 'Volume'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 200.0,
            floating: false,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              title: const Text(
                'Progress Tracker',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      blurRadius: 10.0,
                      color: Colors.black,
                      offset: Offset(0.0, 2.0),
                    ),
                  ],
                ),
              ),
              background: Stack(
                fit: StackFit.expand,
                children: [
                  Image.asset('images/progress_header.jpg', fit: BoxFit.cover),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withAlpha(180),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Track Your Progress',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Select an exercise and metric to view your progress over time',
                          style: TextStyle(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: DropdownButtonFormField<String>(
                                decoration: InputDecoration(
                                  labelText: 'Exercise',
                                  prefixIcon: const Icon(Icons.fitness_center),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                value: _selectedExercise,
                                items:
                                    _exercises.map((exercise) {
                                      return DropdownMenuItem(
                                        value: exercise,
                                        child: Text(exercise),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedExercise = value;
                                    });
                                  }
                                },
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: DropdownButtonFormField<String>(
                                decoration: InputDecoration(
                                  labelText: 'Metric',
                                  prefixIcon: const Icon(Icons.speed),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                value: _selectedMetric,
                                items:
                                    _metrics.map((metric) {
                                      return DropdownMenuItem(
                                        value: metric,
                                        child: Text(metric),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedMetric = value;
                                    });
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: Text(
                      '$_selectedExercise - $_selectedMetric Progress',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  SimpleProgressChart(
                    exercise: _selectedExercise,
                    metric: _selectedMetric,
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Personal Records',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      TextButton.icon(
                        onPressed: () {},
                        icon: const Icon(Icons.add),
                        label: const Text('Add New'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  PersonalRecordCard(
                    exercise: 'Bench Press',
                    weight: 100,
                    date: DateTime.now().subtract(const Duration(days: 7)),
                    imagePath: 'images/bench_press.jpg',
                  ),
                  const SizedBox(height: 12),
                  PersonalRecordCard(
                    exercise: 'Squats',
                    weight: 140,
                    date: DateTime.now().subtract(const Duration(days: 14)),
                    imagePath: 'images/squats.jpg',
                  ),
                  const SizedBox(height: 12),
                  PersonalRecordCard(
                    exercise: 'Deadlifts',
                    weight: 160,
                    date: DateTime.now().subtract(const Duration(days: 3)),
                    imagePath: 'images/deadlift.jpg',
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SimpleProgressChart extends StatelessWidget {
  final String exercise;
  final String metric;

  const SimpleProgressChart({
    super.key,
    required this.exercise,
    required this.metric,
  });

  @override
  Widget build(BuildContext context) {
    // Sample data points
    final List<double> dataPoints = _generateSampleData();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      height: 250,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Weekly Progress',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${dataPoints.last - dataPoints.first > 0 ? '+' : ''}${(dataPoints.last - dataPoints.first).toStringAsFixed(1)} ${metric.toLowerCase()}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(
                dataPoints.length,
                (index) =>
                    _buildBar(context, dataPoints[index], dataPoints, index),
              ),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(
              dataPoints.length,
              (index) => SizedBox(
                width: 30,
                child: Text(
                  'Week ${index + 1}',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBar(
    BuildContext context,
    double value,
    List<double> allValues,
    int index,
  ) {
    // Find the maximum value to scale the bars
    final double maxValue = allValues.reduce((a, b) => a > b ? a : b);
    // Calculate the height percentage
    final double heightPercentage = value / maxValue;

    return Tooltip(
      message: '$value ${metric.toLowerCase()}',
      child: Container(
        width: 30,
        height: 150 * heightPercentage,
        decoration: BoxDecoration(
          color: Colors.blue,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: Text(
            value.toStringAsFixed(0),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  List<double> _generateSampleData() {
    // Sample data points - in a real app, this would be actual workout data
    if (exercise == 'Bench Press' && metric == 'Weight') {
      return [60, 65, 65, 70, 75, 75, 80];
    } else if (exercise == 'Squats' && metric == 'Weight') {
      return [80, 85, 90, 95, 100, 110, 120];
    } else {
      // Default data for other combinations
      return [50, 55, 60, 65, 70, 75, 80];
    }
  }
}

class PersonalRecordCard extends StatelessWidget {
  final String exercise;
  final double weight;
  final DateTime date;
  final String? imagePath;

  const PersonalRecordCard({
    super.key,
    required this.exercise,
    required this.weight,
    required this.date,
    this.imagePath,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 10),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            imagePath != null
                ? Image.asset(
                  imagePath!,
                  width: 40,
                  height: 40,
                  fit: BoxFit.cover,
                )
                : const Icon(Icons.emoji_events, color: Colors.amber, size: 40),
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  exercise,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text('$weight kg'),
                Text(
                  '${date.day}/${date.month}/${date.year}',
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}





import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb; // Remove TargetPlatform if not used

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAmMRZQBHym14SPQjs1PzwnN8io30kjAN0',
    appId: '1:793897974061:web:ec71a8afb68ee298ca3ba4',
    messagingSenderId: '793897974061',
    projectId: 'gym-sync-app',
    authDomain: 'gym-sync-app.firebaseapp.com',
    storageBucket: 'gym-sync-app.firebasestorage.app',
  );
}
